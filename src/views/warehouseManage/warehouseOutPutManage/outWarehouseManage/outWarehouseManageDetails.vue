<template>
  <PageContainer v-loading="pageLoading" :footer="true">
    <div slot="content" class="table-content">
      <div class="content-title">
        <span @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          <span> {{ pageTitle }}</span>
        </span>
      </div>
      <div class="content_box">
        <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
          <span class="green_line"></span>
          <span>单据信息</span>
        </div>
        <div>
          <div class="detailsClass">
            <div class="detail-item">
              单号：<span>{{ formInline.recordNumber || '' }}</span>
            </div>
            <div class="detail-item">
              出库类型：<span>{{ selectedOutboundTypeName || '' }}</span>
            </div>
            <div class="detail-item">
              出库仓库：<span>{{ formInline.warehouseName || '' }}</span>
            </div>
          </div>
          <div class="detailsClass">
            <div class="detail-item">
              出库状态：<span>{{ formInline.status || '' }}</span>
            </div>
            <div class="detail-item">
              出库日期：<span>{{ moment(formInline.outwarehouseDate).format('YYYY-MM-DD ') || '' }}</span>
            </div>
            <div class="detail-item">
              出库金额(元)：<span>{{ formInline.outwarehouseAmountStr || '' }}</span>
            </div>
          </div>
          <div class="detailsClass">
            <div class="detail-item">
              申请人部门：<span>{{ formInline.applicantDepartmentName || '' }}</span>
            </div>
            <div class="detail-item">
              申请人：<span>{{ formInline.applicantName || '' }}</span>
            </div>
            <div class="detail-item">
              申请时间：<span>{{ formInline.createTime || '' }}</span>
            </div>
          </div>
          <div class="detailsClass">
            <div class="detail-item">
              关联工单：<span style="color: #3664dd; cursor: pointer" @click="handleClick(formInline.workId)">{{ formInline.workNum || '' }}</span>
            </div>
            <div class="detail-item">
              摘要信息：<span>{{ formInline.remarks || '' }}</span>
            </div>
            <div class="detail-item">
              <!-- 空占位 -->
            </div>
          </div>
        </div>
        <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
          <span class="green_line"></span>
          <span>明细信息</span>
        </div>
        <div>
          <el-table
            :data="tableData"
            stripe
            :height="(queryParams.type == 'detail' && formInline.status == '已完成') || (queryParams.type == 'review' && formInline.status == '待审核') ? '250px' : '400px'"
            style="width: 100%"
            border
          >
            <el-table-column type="index" label="序号"> </el-table-column>
            <el-table-column prop="materialCode" label="耗材编码" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="materialName" label="耗材名称" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="materialTypeName" label="耗材类型" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="model" label="规格型号" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="basicUnitName" label="计量单位" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="brandName" label="品牌" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="operateCount" label="出库数量"> </el-table-column>
            <el-table-column prop="unitPriceStr" label="单价" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="sumAmountStr" label="金额"> </el-table-column>
          </el-table>
        </div>
        <div
          v-if="(queryParams.type == 'detail' && formInline.status == '已完成') || (queryParams.type == 'review' && formInline.status == '待审核')"
          class="toptip"
          style="padding: 0; border: 0; font-weight: bold"
        >
          <span class="green_line"></span>
          <span>审核信息</span>
        </div>
        <div v-if="queryParams.type == 'review'">
          <el-form ref="form" :model="form" label-width="100px" :rules="rules">
            <el-form-item label="审核结果" prop="type">
              <el-radio v-model="form.type" label="1">通过</el-radio>
              <el-radio v-model="form.type" label="0">挂起</el-radio>
            </el-form-item>
            <el-form-item label="审核意见" prop="opinion">
              <el-input v-model="form.opinion" maxlength="100" show-word-limit type="textarea" placeholder="请输入" style="width: 70%" />
            </el-form-item>
          </el-form>
        </div>
        <div v-if="queryParams.type == 'detail' && (formInline.status == '已完成' || formInline.status == '已挂起')">
          <div class="detailsClass">
            <div class="detail-item">
              审核结果：<span>{{ approvalInfo.type }}</span>
            </div>
            <div class="detail-item">
              审核人：<span>{{ approvalInfo.warehouseManagerName }}</span>
            </div>
            <div class="detail-item">
              审核时间：<span>{{ approvalInfo.approvalTime }}</span>
            </div>
          </div>
          <div class="detailsClass">
            <div class="detail-item">
              审核意见：<span>{{ approvalInfo.opinion }}</span>
            </div>
          </div>
        </div>
      </div>
      <template v-if="workOrderDetailCenterShow">
        <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
          <template slot="title">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </template>
          <workOrderDetailList :rowData="detailObj" @close="workOrderDetailCloseDialog" />
        </el-dialog>
      </template>
    </div>
    <div slot="footer">
      <el-button type="" @click="$router.go(-1)">取消</el-button>
      <el-button v-if="queryParams.type == 'review'" type="primary" :loading="formLoading" @click="submitForm()">提交</el-button>
    </div>
  </PageContainer>
</template>
<script>
import workOrderDetailList from '@/views/serviceQuality/maintenance/components/workOrderDetailList.vue'
import moment from 'moment'
export default {
  name: 'outWarehouseManageDetails',
  components: {
    workOrderDetailList
  },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        detail: '出库单详情',
        review: '出库单审核'
      }
      to.meta.title = typeList[to.query.type] ?? '出库单详情'
    }
    next()
  },
  data() {
    return {
      moment,
      queryParams: {},
      tableData: [],
      formInline: {},
      form: {
        type: '1',
        opinion: ''
      },
      pageLoading: false,
      rules: {
        opinion: [{ required: false, message: '请输入审核意见', trigger: 'blur' }]
      },
      outboundTypeList: [],
      formLoading: false,
      warehouseApprovalRecordList: [],
      detailObj: {},
      dialogTitle: '',
      workOrderDetailCenterShow: false
    }
  },
  computed: {
    pageTitle() {
      let title = '出库单'
      switch (this.queryParams.type) {
        case 'review':
          title = title + '审核'
          break
        case 'detail':
          title += '详情'
          break
      }
      return title
    },
    selectedOutboundTypeName() {
      const type = this.outboundTypeList.find((item) => item.dictionaryDetailsId === this.formInline.outwarehouseType)
      return type ? type.dictionaryDetailsName : ''
    },
    approvalInfo() {
      const record = this.warehouseApprovalRecordList && this.warehouseApprovalRecordList.length ? this.warehouseApprovalRecordList[0] : {}
      return {
        type: record.type || '',
        warehouseManagerName: record.warehouseManagerName || '',
        approvalTime: record.approvalTime ? this.moment(record.approvalTime).format('YYYY-MM-DD HH:mm:ss') : '',
        opinion: record.opinion || ''
      }
    }
  },
  watch: {
    'form.type'(newVal) {
      console.log(newVal, 'newVal')
      if (newVal === '0') {
        this.rules.opinion[0].required = true
      } else {
        this.rules.opinion[0].required = false
      }
    }
  },
  created() {},
  mounted() {
    this.getWarehouseType()
    this.queryParams = this.$route.query
    this.getById()
  },
  methods: {
    handleClick(e) {
      this.detailObj = {
        id: e,
        // 标记这是查看模式，需要隐藏处理节点
        isViewMode: true
      }
      this.$api.getWorkOrderDetail({ id: e, operSource: 'souye' }).then((res) => {
        if (res) {
          this.dialogTitle = `${res.customizeTaskConfiguration.workTypeName}（${res.olgTaskManagement.flowtype}）`
          this.workOrderDetailCenterShow = true
        }
      })
    },
    // 关闭工单详情
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
    },
    // 获取出库类型
    getWarehouseType() {
      const params = {
        pageSize: 99999,
        currentPage: 1,
        userType: 1,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        dictionaryCategoryId: 'outbound_type',
        dictionaryDetailsStatus: '1'
      }
      this.$api.warehouseApi.getDictByPage(params).then((res) => {
        if (res.code == '200') {
          this.outboundTypeList = res.data
        } else {
          throw res.msg || res.message
        }
      })
    },
    getById() {
      this.pageLoading = true
      let params = {
        id: this.queryParams.id || '',
        recordNumber: this.queryParams.recordNumber || '',
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.warehouseApi.getOutWarehouseRecordById(params).then((res) => {
        try {
          if (res.code == 200) {
            const data = res.data
            this.formInline = data
            this.tableData = data.materialRecordList
            this.warehouseApprovalRecordList = data.warehouseApprovalRecordList
            this.pageLoading = false
          }
        } catch (error) {
          this.pageLoading = false
        }
      })
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          let param = {
            ...this.form,
            id: this.queryParams.id,
            recordNumber: this.queryParams.recordNumber,
            userName: this.$store.state.user.userInfo.user.staffName,
            userId: this.$store.state.user.userInfo.user.staffId
          }
          this.$api.warehouseApi
            .approveOutWarehouseRecord(param)
            .then((res) => {
              this.formLoading = false
              if (res.code == '200') {
                this.$message({
                  message: '审核成功',
                  type: 'success'
                })
                this.$router.go(-1)
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
            })
            .catch((msg) => this.$message.error(msg || '审核失败'))
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}
.content_box {
  height: calc(100% - 30px);
  padding: 0 24px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  .detailsClass {
    display: flex;
    gap: 20px; // 元素间距
    width: 100%;
    .detail-item {
      flex: 1; // 平均分配宽度
      min-width: 33%; // 确保每行三个
      padding: 8px;
      box-sizing: border-box;
      margin-bottom: 16px;
    }
  }
}
.content-title {
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 24px;
  cursor: pointer;
}
.green_line {
  display: inline-block;
  width: 5px;
  height: 16px;
  background: #3562db;
  margin-right: 6px;
  border-radius: 2px !important;
  vertical-align: middle;
}
.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  padding-left: 16px;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}
/* 工单详情弹框样式 */
::v-deep .detailDialog {
  .el-dialog__header {
    border-bottom: 1px solid #e6e6e6;
    padding: 14px 20px 12px;
    span {
      font-size: 16px;
    }
  }
  .el-dialog__body {
    height: 70vh;
    overflow-y: auto;
    padding: 15px;
  }
  .el-dialog__headerbtn {
    top: 13px;
    font-size: 18px;
  }
}
</style>
