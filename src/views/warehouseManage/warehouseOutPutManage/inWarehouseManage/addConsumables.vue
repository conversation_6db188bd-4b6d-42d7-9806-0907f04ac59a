<template>
  <div class="main">
    <el-dialog :title="warehouseId ? '选择库存耗材' : '新增耗材'" :visible.sync="dialogVisible" :before-close="closeDialog" append-to-body custom-class="model-dialog" width="60%">
      <div class="outermost">
        <div class="left">
          <el-input v-model.trim="filterText" style="margin: 5px 0" clearable placeholder="输入关键字进行过滤"></el-input>
          <div class="block">
            <el-tree
              ref="treeRef"
              v-loading="treeLoading"
              :data="data"
              :expand-on-click-node="false"
              node-key="id"
              :props="defaultProps"
              default-expand-all
              highlight-current
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
        </div>
        <div v-loading="loading" class="content">
          <div class="topTools">
            <el-input v-model.trim="keyWords" placeholder="耗材编码/耗材名称" style="width: 200px; margin-right: 10px"></el-input>
            <el-button type="primary" @click="reset">重 置</el-button>
            <el-button type="primary" @click="search">查 询</el-button>
            <span v-if="warehouseId" style="margin-left: 10px; color: #409eff; font-size: 12px"> <i class="el-icon-info"></i> 仅显示当前仓库有库存的耗材 </span>
          </div>
          <el-table ref="personTable" v-loading="tableLoading" :data="tableData" border height="90%" stripe :row-key="getRowKeys" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column type="index" label="序号" width="75">
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="耗材编码" prop="materialCode" show-overflow-tooltip> </el-table-column>
            <el-table-column label="耗材名称" prop="materialName" show-overflow-tooltip></el-table-column>
            <el-table-column label="耗材类型" prop="materialTypeName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="规格型号" prop="model" show-overflow-tooltip> </el-table-column>
            <el-table-column label="计量单位" prop="basicUnitName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="品牌" prop="brandName" show-overflow-tooltip> </el-table-column>
            <el-table-column v-if="warehouseId" label="当前库存" prop="num" show-overflow-tooltip> </el-table-column>
            <el-table-column label="单价(元)" prop="unitPriceStr" show-overflow-tooltip> </el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-pagination
          class="user-pagination"
          style
          :current-page="currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :pager-count="5"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
        <el-button type="primary" @click="closeDialog">取 消</el-button>
        <el-button type="primary" :disabled="addDis" class="sino-button-sure" @click="editSubmit">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { transData } from '@/util'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    tableDate: {},
    warehouseId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      filterText: '',
      keyWords: '',
      total: 0,
      currentPage: 1,
      treeDataTop: '',
      data: [],
      pageSize: 10,
      tableLoading: false,
      treeLoading: false,
      multipleSelection: [],
      defaultProps: {
        label: 'dictionaryDetailsName',
        children: 'children'
      },
      tableData: [],
      loading: false,
      addDis: false
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 525
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val)
    },
    warehouseId(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.currentPage = 1
        this.search()
      }
    }
  },
  mounted() {
    this.treeDataTop = ''
    this.init()
    this.search()
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    editSubmit() {
      if (this.multipleSelection.length > 0) {
        this.$emit('handleConsumablesSelect', this.multipleSelection)
      } else {
        this.$message.error('请先选择耗材')
      }
    },
    init() {
      this.treeLoading = true
      this.$api.warehouseApi.getDictByPage({ dictionaryCategoryId: 'consumables_type', dictionaryDetailsStatus: '1' }).then((res) => {
        this.treeLoading = false
        if (res.code == '200') {
          const treeData = transData(res.data, 'id', 'parentId', 'children')
          const root = { dictionaryDetailsName: '全部', id: '', children: [] }
          root.children = treeData.map((it) => {
            it.parentId = root.id
            return it
          })
          this.data = [root]
          this.treeDataTop = root.id
          this.$nextTick(() => {
            this.$refs.treeRef.setCurrentKey(this.treeDataTop)
          })
        }
      })
    },
    asveActiveTitle(i) {
      this.treeDataTop = ''
      this.init()
      this.search()
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.search()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.search()
    },
    handleNodeClick(data) {
      this.currentPage = 1
      this.treeDataTop = data.id
      this.keyWords = ''
      this.search()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    search() {
      const params = {
        pageSize: this.pageSize,
        CurrentPage: this.currentPage,
        keyWords: this.keyWords,
        materialTypeCode: this.treeDataTop
      }

      // 如果有仓库ID，则查询该仓库的库存数据，否则查询所有耗材
      if (this.warehouseId) {
        params.warehouseId = this.warehouseId
        this.tableLoading = true
        this.$api.warehouseApi.getOutConsumablePage(params).then((res) => {
          this.tableLoading = false
          // 为每个耗材项添加unitPriceStr字段
          this.tableData = res.data.list
          this.total = res.data.sum
        })
      } else {
        params.status = '0' // 只获取已启用状态的耗材
        this.tableLoading = true
        this.$api.warehouseApi.getConsumableByPage(params).then((res) => {
          this.tableLoading = false
          // 为每个耗材项添加unitPriceStr字段
          this.tableData = res.data.list
          this.total = res.data.sum
        })
      }
    },
    reset() {
      this.currentPage = 1
      this.treeDataTop = ''
      this.$refs.treeRef.setCurrentKey(null) // 清除树的选中状态
      this.keyWords = ''
      this.search()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.dictionaryDetailsName.indexOf(value) !== -1
    }
  }
}
</script>
<style lang="scss" scoped>
.outermost {
  display: flex;
  width: 100%;
  height: 500px;
  border: 1px solid #eee;
  padding: 10px;
}
.left {
  padding: 10px;
  width: 268px;
  margin-right: 10px;
  height: 100%;
  background-color: #fff;
  .left_content {
    height: calc(100% - 60px);
    overflow: auto;
  }
  &::v-deep(.el-tree) {
    height: calc(100%);
    overflow: auto;
    padding: 8px 0;
    .el-tree-node__content {
      line-height: 36px;
      height: 36px;
    }
    .el-tree-node.is-current > .el-tree-node__content {
      color: #3562db;
      background: #e6effc;
    }
  }
}
.content {
  background-color: #fff;
  padding: 10px;
  width: calc(100% - 278px);
  height: 100%;
}
.topTools {
  margin-bottom: 5px;
}
.user-pagination {
  text-align: right;
  position: absolute;
  right: 250px;
  bottom: 10px;
}
// @media screen and(max-width: 1600px) {
//   .user-pagination {
//     right: 210px;
//     bottom: 10px;
//   }
// }
.tree-title {
  border: 1px solid #ebeef5;
  width: 40%;
  margin-bottom: 5px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  color: #5188fc;
  cursor: pointer;
}
.tree-title-active {
  background: #5188fc;
  border-color: #5188fc;
  color: #fff;
}
.block {
  height: calc(100% - 80px);
  overflow: auto;
}
::v-deep .el-tree-node__label {
  display: inline-block;
  white-space: normal;
  text-align: left;
}
::v-deep .el-tree-node__content {
  height: auto;
}
</style>
