<template>
  <PageContainer v-loading="pageLoading" :footer="true">
    <div slot="content" class="table-content">
      <div class="content-title">
        <span @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          <span> {{ pageTitle }}</span>
        </span>
      </div>
      <div class="content_box">
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="120px" :rules="rules">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            <span>单据信息</span>
          </div>
          <el-form-item label="入库单号：" prop="recordNumber">
            <el-input v-model.trim="formInline.recordNumber" maxlength="100" show-word-limit type="text" placeholder="自动生成" disabled></el-input>
          </el-form-item>
          <el-form-item label="入库类型：" prop="inwarehouseType">
            <el-select v-model.trim="formInline.inwarehouseType" filterable placeholder="入库类型">
              <el-option v-for="item in inboundTypeList" :key="item.dictionaryDetailsId" :label="item.dictionaryDetailsName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入库仓库：" prop="warehouseId">
            <el-select v-model.trim="formInline.warehouseId" filterable placeholder="入库仓库">
              <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入库日期：" prop="inwarehouseDate">
            <el-date-picker v-model="formInline.inwarehouseDate" type="date" value-format="yyyy-MM-dd" placeholder="入库日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="入库金额(元)：" prop="inwarehouseAmount">
            <el-input v-model.trim="formInline.inwarehouseAmount" maxlength="30" show-word-limit type="text" placeholder="自动生成" disabled></el-input>
          </el-form-item>
          <el-form-item label="申请人部门：" prop="applicantDepartmentId">
            <el-cascader
              v-model="formInline.applicantDepartmentId"
              placeholder="请选择部门"
              :options="deptList"
              :props="deptTree"
              :show-all-levels="false"
              clearable
              filterable
              style="width: 100%"
              @change="selectDept"
            >
            </el-cascader>
          </el-form-item>
          <el-form-item label="申请人：" prop="applicantId">
            <el-select v-model="formInline.applicantId" placeholder="请选择人员" clearable filterable style="width: 100%">
              <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关联工单：" prop="professionalCategoryCode">
            <el-input v-model.trim="formInline.workNum" type="text" placeholder="关联工单" @click.native="showWorkOrder"></el-input>
          </el-form-item>
          <el-form-item label="关联出库单：" prop="professionalCategoryCode">
            <el-input v-model.trim="formInline.outwarehouseRecordNumber" type="text" placeholder="关联出库单" @click.native="showWorkOrderOutbound"></el-input>
          </el-form-item>
          <el-form-item style="display: block" label="摘要信息：" prop="remarks">
            <el-input v-model.trim="formInline.remarks" maxlength="100" show-word-limit type="textarea" placeholder="请输入" class="cascaderWid"></el-input>
          </el-form-item>
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            明细信息
          </div>
          <el-button type="primary" @click="showaddConsumables">新增</el-button>
          <el-button type="primary" @click="batchImport">批量导入</el-button>
          <el-button type="danger" @click="handleBatchDelete()">批量删除</el-button>
          <div v-if="tableData.length > 0">
            <el-table :data="tableData" stripe height="400px" style="width: 100%; margin-top: 16px" border @selection-change="handleSelectionChange">
              <el-table-column type="selection"> </el-table-column>
              <el-table-column type="index" label="序号"> </el-table-column>
              <el-table-column prop="materialCode" label="耗材编码" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="materialName" label="耗材名称" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="materialTypeName" label="耗材类型" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="model" label="规格型号" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="basicUnitName" label="计量单位" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="brandName" label="品牌" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="operateCount" label="入库数量" width="200">
                <template v-slot="{ row }">
                  <el-input
                    v-model.number="row.operateCount"
                    style="width: 80%"
                    placeholder="请输入数量"
                    maxlength="8"
                    @change="handleQuantityChange(row)"
                    @input="row.operateCount = Math.abs(String(row.operateCount).replace(/[^\d]/g, '')) || ''"
                    @blur="validateQuantity(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="unitPriceStr" label="单价(元)" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="sumAmount" label="金额"> </el-table-column>
              <el-table-column label="操作">
                <template #default="{ row }">
                  <el-button type="text" style="color: red" @click="onOperate(row)"> 删除 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <!--耗材-->
      <addConsumables
        v-if="dialogVisible"
        ref="addControlRow"
        :dialogVisible="dialogVisible"
        @handleConsumablesSelect="handleConsumablesSelect"
        @closeDialog="closeDialog"
      ></addConsumables>
      <!--关联工单-->
      <workOrderAdd ref="addControlRow" :workOrderVisible="workOrderVisible" @handleworkOrder="handleworkOrder" @closeWorkOrderDialog="closeWorkOrderDialog"></workOrderAdd>
      <warehouse ref="warehouse" :warehouseVisible="warehouseVisible" @handlewarehouse="handlewarehouse" @closeWarehouseDialog="closeWarehouseDialog"></warehouse>
      <!--批量导入弹窗-->
      <el-dialog title="批量导入" :visible.sync="dialogVisibleExport" width="30%">
        <div>
          <div style="display: flex; align-items: center; gap: 10px">
            <el-upload
              action=""
              style="display: inline-block"
              :file-list="fileList"
              :limit="1"
              :before-upload="beforeAvatarUpload"
              :http-request="(file) => httpRequset(file)"
              :on-remove="(file, fileList) => handleRemove(file, fileList)"
            >
              <span style="white-space: nowrap">上传附件：</span>
              <el-button :disabled="Boolean(fileUrl)" size="small" type="primary">点击上传</el-button>
              <span style="font-size: 14px; color: #2aa4d9; margin-left: 20px" @click.stop="exportImportTemplete">模板下载</span>
              <div style="margin-top: 15px">支持扩展名.xls/.xlsx</div>
            </el-upload>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="exportCatch != 0" @click="failureRecord">失败记录</el-button>
          <el-button @click="dialogVisibleExport = false">取 消</el-button>
          <el-button type="primary" @click="dialogVisibleExport = false">确 定</el-button>
        </span>
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button plain @click="$router.go(-1)">取消</el-button>
      <el-button @click="saveForm(1)">暂存</el-button>
      <el-button type="primary" :loading="formLoading" @click="saveForm(2)">提交</el-button>
    </div>
  </PageContainer>
</template>
<script>
import addConsumables from './addConsumables.vue'
import workOrderAdd from './workOrderAdd.vue'
import warehouse from './warehouse.vue'
import addControlRow from '@/views/sysManagement/auth/unifiedPermissions/addControlRow.vue'
import axios from 'axios'
import moment from 'moment'
export default {
  name: 'inWarehouseManageAdd',
  components: { addControlRow, addConsumables, workOrderAdd, warehouse },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新建入库单',
        edit: '编辑入库单'
      }
      to.meta.title = typeList[to.query.type] ?? '新增入库单'
    }
    next()
  },
  data() {
    return {
      formLoading: false,
      pageLoading: false,
      warehouseList: [],
      inboundTypeList: [],
      personList: [],
      warehouseVisible: false,
      fileList: [],
      dialogVisibleExport: false,
      multipleSelection: [], // 新增多选存储
      tableData: [],
      dialogVisible: false,
      workOrderVisible: false,
      routeQueryType: '',
      userDepartmentProps: {
        children: 'children',
        label: 'deptName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      majorList: [],
      useDeptList: [],
      formInline: {
        workNum: '',
        workId: '',
        outwarehouseRecordNumber: '',
        professionalCategoryCode: '',
        assetsRemarks: '', // 资产备注说明
        remarks: '' // 备注
      },
      fileUrl: '',
      fileName: '',
      dataName: '',
      exportCatch: 0,
      fileCatch: {},
      rules: {
        inwarehouseType: [{ required: true, message: '请选择入库类型', trigger: 'change' }],
        warehouseId: [{ required: true, message: '请选择入库仓库', trigger: 'change' }],
        inwarehouseDate: [{ required: true, message: '请选择入库日期', trigger: 'change' }],
        applicantDepartmentId: [{ required: true, message: '请选择申请人部门', trigger: 'change' }],
        applicantId: [{ required: true, message: '请选择申请人', trigger: 'change' }]
      },
      userInfo: {},
      deptList: [],
      deptTree: {
        value: 'id',
        label: 'deptName',
        children: 'children',
        multiple: false,
        checkStrictly: true
      }
    }
  },
  computed: {
    // 新增计算属性
    totalAmount() {
      return this.tableData
        .reduce((sum, row) => {
          return sum + (Number(row.sumAmount) || 0)
        }, 0)
        .toFixed(2)
    },
    pageTitle() {
      let title = '入库单'
      switch (this.routeQueryType) {
        case 'add':
          title = '新增' + title
          break
        case 'edit':
          title = '编辑' + title
          break
      }
      return title
    }
  },
  watch: {
    // 监听计算属性变化更新表单字段
    totalAmount(newVal) {
      this.$set(this.formInline, 'inwarehouseAmount', newVal)
    }
  },
  created() {
    this.userInfo = this.$store.state.user.userInfo.user
  },
  mounted() {
    this.getWarehouseList()
    this.getWarehouseType()
    this.getDeptList()
    this.routeQueryType = this.$route.query.type
    // 如果是编辑模式，加载入库单数据
    if (this.routeQueryType === 'edit' && this.$route.query.id) {
      this.getInWarehouseRecordById()
    }
  },
  methods: {
    // 获取入库单详情
    getInWarehouseRecordById() {
      this.pageLoading = true
      let params = {
        id: this.$route.query.id || '',
        userName: this.userInfo.staffName,
        userId: this.userInfo.staffId,
        recordNumber: this.$route.query.recordNumber || ''
      }
      this.$api.warehouseApi.getInWarehouseRecordById(params).then((res) => {
        try {
          if (res.code == 200) {
            const data = res.data
            // 填充表单数据 - 只保留新增时需要的字段
            this.formInline = {
              // 基本信息
              recordNumber: data.recordNumber || '',
              inwarehouseType: data.inwarehouseType || '',
              warehouseId: data.warehouseId || '',
              inwarehouseDate: data.inwarehouseDate ? moment(data.inwarehouseDate).format('YYYY-MM-DD') : '',
              inwarehouseAmount: data.inwarehouseAmount || '',
              applicantDepartmentId: data.applicantDepartmentId ? [data.applicantDepartmentId] : [],
              applicantId: data.applicantId || '',
              workNum: data.workNum || '',
              workId: data.workId || '',
              outwarehouseRecordNumber: data.outwarehouseRecordNumber || '',
              remarks: data.remarks || ''
            }
            // 填充表格数据
            if (data.materialRecordList && data.materialRecordList.length > 0) {
              this.tableData = data.materialRecordList.map((item) => ({
                id: item.id,
                materialCode: item.materialCode,
                materialName: item.materialName,
                materialTypeName: item.materialTypeName,
                model: item.model,
                basicUnitName: item.basicUnitName,
                brandName: item.brandName || '',
                operateCount: item.operateCount || '',
                unitPrice: item.unitPrice || '',
                unitPriceStr: item.unitPriceStr || '',
                sumAmount: item.sumAmountStr || ''
              }))
            }
            // 加载部门人员数据
            if (this.formInline.applicantDepartmentId && this.formInline.applicantDepartmentId.length > 0) {
              this.getPersonList(this.formInline.applicantDepartmentId)
            }
          }
        } catch (error) {
          this.$message.error('获取入库单详情失败')
        } finally {
          this.pageLoading = false
        }
      })
    },
    // 提交
    saveForm(status) {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          // 检查表格数据是否存在
          if (this.tableData.length === 0) {
            this.$message.error('请添加入库物品')
            return
          }
          // 检查每行的入库数量是否已填写
          const emptyCountRows = this.tableData.filter((row) => !row.operateCount)
          if (emptyCountRows.length > 0) {
            this.$message.error('请填写所有行的入库数量')
            return
          }
          // 获取选中的仓库对象
          const selectedWarehouse = this.warehouseList.find((item) => item.id === this.formInline.warehouseId)
          // 获取申请人名称
          const selectedPerson = this.personList.find((item) => item.id === this.formInline.applicantId)
          // 获取选中的入库类型对象
          const selectedInboundType = this.inboundTypeList.find((item) => item.id === this.formInline.inwarehouseType)
          // 获取部门名称（处理级联选择器的数组值）
          const findDeptName = (idList, list) => {
            const targetId = idList?.slice(-1)[0] // 取最后一级部门ID
            let result = ''
            const search = (arr) => {
              arr.forEach((item) => {
                if (item.id === targetId) result = item.deptName
                if (item.children) search(item.children)
              })
            }
            search(list)
            return result
          }
          this.formLoading = true
          // 构建提交参数，只包含必要的字段
          let param = {
            // 基本信息
            recordNumber: this.formInline.recordNumber || '', // 入库单号
            inwarehouseType: this.formInline.inwarehouseType || '', // 入库类型ID
            warehouseId: this.formInline.warehouseId || '', // 入库仓库ID
            inwarehouseDate: this.formInline.inwarehouseDate || '', // 入库日期
            inwarehouseAmount: this.formInline.inwarehouseAmount || '', // 入库金额(元)
            applicantDepartmentId: this.formInline.applicantDepartmentId ? this.formInline.applicantDepartmentId[this.formInline.applicantDepartmentId.length - 1] : '', // 申请人部门ID
            applicantId: this.formInline.applicantId || '', // 申请人ID
            workNum: this.formInline.workNum || '', // 关联工单号
            workId: this.formInline.workId || '', // 关联工单ID
            outwarehouseRecordNumber: this.formInline.outwarehouseRecordNumber || '', // 关联出库单号
            remarks: this.formInline.remarks || '', // 摘要信息/备注
            // 处理后的数据
            materialRecordArrayStr: JSON.stringify(this.tableData), // 入库物品明细数据(JSON字符串)
            userId: this.userInfo.staffId, // 当前操作用户ID
            userName: this.userInfo.staffName, // 当前操作用户姓名
            status: status, // 单据状态(0:暂存 1:提交)
            createSource: '0', // 创建来源(0:PC端)
            warehouseName: selectedWarehouse?.warehouseName || '', // 入库仓库名称
            inwarehouseTypeName: selectedInboundType?.dictionaryDetailsName || '', // 入库类型名称
            applicantName: selectedPerson?.staffName || '', // 申请人姓名
            applicantDepartmentName: findDeptName(this.formInline.applicantDepartmentId, this.deptList) // 申请人部门名称
          }
          // 如果是编辑模式，确保传递id
          if (this.routeQueryType === 'edit' && this.$route.query.id) {
            param.id = this.$route.query.id
          }
          this.$api.warehouseApi
            .inwarehouseRecordSave(param)
            .then((res) => {
              this.formLoading = false
              if (res.code == '200') {
                this.$message({
                  message: status === 1 ? '暂存成功' : '保存成功',
                  type: 'success'
                })
                this.$router.go(-1)
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
            })
            .catch((msg) => this.$message.error(msg || (status === 1 ? '暂存失败' : '保存失败')))
        }
      })
    },
    // 获取仓库列表
    getWarehouseList() {
      let param = {
        pageSize: 99999,
        CurrentPage: 1,
        status: '0'
      }
      this.$api.warehouseApi.getWarehouseByPage(param).then((res) => {
        if (res.code == 200) {
          this.warehouseList = res.data.list
        }
      })
    },
    // 获取部门
    getDeptList() {
      return new Promise((resolve) => {
        this.$api
          .getDeptList()
          .then((res) => {
            if (res.code == 200) {
              this.allDept = res.data
              this.deptList = this.transformDeptData(res.data, 'id', 'pid', 'children')
            }
            resolve()
          })
          .catch(() => {
            resolve() // 即使失败也要resolve
          })
      })
    },
    // 部门数据转换为树形结构
    transformDeptData(data, id, pid, children) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.log('部门数据为空或格式不正确')
        return []
      }
      const res = []
      const temp = {}
      try {
        // 第一次遍历，建立id到节点的映射
        data.forEach((item) => {
          if (item && item[id]) {
            temp[item[id]] = { ...item }
          }
        })
        // 第二次遍历，建立父子关系
        data.forEach((item) => {
          if (!item) return
          const itemId = item[id]
          const tempPid = item[pid]
          // 检查是否有有效的父ID，且父ID存在于数据中
          if (tempPid && tempPid !== '0' && tempPid !== itemId && temp[tempPid]) {
            if (!temp[tempPid][children]) {
              temp[tempPid][children] = []
            }
            temp[tempPid][children].push(temp[itemId])
          } else {
            // 根节点或父节点不存在的情况
            if (temp[itemId]) {
              res.push(temp[itemId])
            }
          }
        })
        return res
      } catch (error) {
        return []
      }
    },
    selectDept(val) {
      console.log('选择的部门值:', val)
      const deptArr = []
      if (val && val.length > 0) {
        // 现在是单选，直接取最后一个ID（叶子节点）
        const leafId = val[val.length - 1]
        deptArr.push(leafId)
      }
      console.log('最终选择的部门IDs:', deptArr)
      if (deptArr.length > 0) {
        this.getPersonList(deptArr)
      } else {
        this.personList = []
        this.formInline.applicantId = ''
      }
    },
    // 获取人员
    getPersonList(deptIds) {
      // 创建请求参数
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: ''
      }
      // 如果传入了部门ID参数
      if (deptIds && Array.isArray(deptIds) && deptIds.length > 0) {
        params.officeId = deptIds.join(',')
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
          if (this.personList.length == 0) {
            this.$set(this.formInline, 'applicantId', '')
          } else {
            // 如果当前已选择的申请人不在新的人员列表中，则清空选择
            if (this.formInline.applicantId) {
              const isValidApplicant = this.personList.some((person) => person.id === this.formInline.applicantId)
              if (!isValidApplicant) {
                this.$set(this.formInline, 'applicantId', '')
              }
            }
          }
        }
      })
    },
    // 获取入库类型
    getWarehouseType() {
      const params = {
        pageSize: 99999,
        currentPage: 1,
        userType: 1,
        userId: this.userInfo.staffId,
        userName: this.userInfo.staffName,
        dictionaryCategoryId: 'store_type',
        dictionaryDetailsStatus: '1'
      }
      this.$api.warehouseApi.getDictByPage(params).then((res) => {
        if (res.code == '200') {
          this.inboundTypeList = res.data
        } else {
          throw res.msg || res.message
        }
      })
    },
    // 打开关联工单
    showWorkOrder() {
      this.workOrderVisible = true
    },
    // 打开关联出库单
    showWorkOrderOutbound() {
      this.warehouseVisible = true
    },
    beforeAvatarUpload(file) {
      // const fileSize = file.size / 1024 / 1024 < 100
      // if (!fileSize) {
      //   this.$message.error('上传附件大小不能超过 100MB!')
      // }
      // return fileSize
      // 验证文件类型
      const extension = file.name.split('.').pop().toLowerCase()
      const isValidType = ['xls', 'xlsx'].includes(extension)
      if (!isValidType) {
        this.$message.error('只能上传.xls或.xlsx格式文件')
        return false
      }
      return true
    },
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      // 从store中获取用户信息，如果store中没有则从LOGINDATA中获取
      let hospitalCode, unitCode
      if (this.$store.getters['user/isLogin']) {
        const userInfo = this.$store.state.user.userInfo.user
        hospitalCode = userInfo.hospitalCode ?? 'BJSJTYY'
        unitCode = userInfo.unitCode ?? 'BJSYGJ'
      } else {
        const loginData = JSON.parse(sessionStorage.getItem('LOGINDATA') || '{}')
        hospitalCode = loginData.hospitalCode || 'BJSJTYY'
        unitCode = loginData.unitCode || 'BJSYGJ'
      }
      params.append('hospitalCode', hospitalCode)
      params.append('unitCode', unitCode)
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'inwarehouseRecord/importConsumableExcelCheck',
        data: params,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          if (res.code == 200) {
            this.fileUrl = res.data.fileKey
            this.fileName = res.data.name
            this.fileList = [
              {
                name: res.data.name,
                url: res.data.fileKey
              }
            ]
            if (!this.dataName) {
              this.dataName = res.data.name.replace(/\.[^/.]+$/, '')
            }
            this.$message({
              message: '上传成功',
              type: 'success'
            })
          } else {
            const failNumber = +res.data.msg.match(/导入失败(\d+)条/)[1]
            this.exportCatch = failNumber
            this.fileCatch = file
            this.fileList = []
            failNumber > 0 ? this.$message.error(res.data.msg) : this.$message.success(res.data.msg)
            this.tableData = this.tableData.concat(res.data.data.list)
          }
        })
        .catch((err) => {
          this.fileList = []
          this.$message({
            message: '上传失败',
            type: 'error'
          })
        })
    },
    // 下载失败记录
    failureRecord() {
      // 从store中获取用户信息，如果store中没有则从LOGINDATA中获取
      let hospitalCode, unitCode
      if (this.$store.getters['user/isLogin']) {
        const userInfo = this.$store.state.user.userInfo.user
        hospitalCode = userInfo.hospitalCode ?? 'BJSJTYY'
        unitCode = userInfo.unitCode ?? 'BJSYGJ'
      } else {
        const baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA') || '{}')
        hospitalCode = baseInfo.hospitalCode || 'BJSJTYY'
        unitCode = baseInfo.unitCode || 'BJSYGJ'
      }
      const params = new FormData()
      params.append('file', this.fileCatch.file)
      // 将单位参数也加入FormData
      params.append('unitCode', unitCode)
      params.append('hospitalCode', hospitalCode)
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'inwarehouseRecord/importConsumableExcelDownLoad',
        data: params,
        responseType: 'arraybuffer',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        if (res.status == 200 && !res.data.code) {
          let name = '导入失败记录.xls'
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          // let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          let url = URL.createObjectURL(blob)
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
          if (res.data.code == 500) {
            this.$message.error(res.data.message)
          } else if (res.status == 200 && !res.data.code) {
            this.$message.success('下载成功')
            this.exportCatch = 0
          } else {
            this.$message.error('下载失败')
          }
        }
      })
    },
    handleRemove(file, fileList) {
      this.fileUrl = ''
      this.fileList = []
    },
    // 批量导入
    batchImport() {
      this.dialogVisibleExport = true
    },
    // 处理多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 耗材数据
    handleConsumablesSelect(selectedData) {
      this.dialogVisible = false
      this.tableData = this.tableData.concat(
        selectedData.map((item) => ({
          ...item,
          operateCount: '', // 初始化入库数量为0
          sumAmount: '' // 初始化入库数量为0
        }))
      )
    },
    // 关联工单数据
    handleworkOrder(data) {
      this.formInline.workNum = data[0].workNum
      this.formInline.workId = data[0].id
      this.workOrderVisible = false
    },
    // 关联出库单数据
    handlewarehouse(data) {
      console.log('关联出库单数据', data)
      this.formInline.outwarehouseRecordNumber = data[0].recordNumber
      // 如果出库单有关联工单号且当前入库单没有关联工单号，则自动填充
      if (data[0].workNum) {
        this.formInline.workNum = data[0].workNum
        this.formInline.workId = data[0].workId || ''
      }

      // 获取出库单详情，填充耗材明细和其他信息
      this.getOutWarehouseDetail(data[0].id, data[0].recordNumber)

      this.warehouseVisible = false
    },

    // 获取出库单详情并填充耗材明细
    getOutWarehouseDetail(id, recordNumber) {
      this.pageLoading = true
      let params = {
        id: id || '',
        userName: this.userInfo.staffName,
        userId: this.userInfo.staffId,
        recordNumber: recordNumber || ''
      }

      this.$api.warehouseApi
        .getOutWarehouseRecordById(params)
        .then((res) => {
          try {
            if (res.code == 200) {
              const data = res.data

              // 自动填充入库仓库、申请人部门、申请人和日期
              this.autoFillFromOutWarehouse(data)

              // 填充表格数据
              if (data.materialRecordList && data.materialRecordList.length > 0) {
                // 将出库单的耗材明细添加到入库单的明细中
                const newMaterials = data.materialRecordList.map((item) => ({
                  id: item.id,
                  materialCode: item.materialCode,
                  materialName: item.materialName,
                  materialTypeName: item.materialTypeName,
                  model: item.model,
                  basicUnitName: item.basicUnitName,
                  brandName: item.brandName || '',
                  operateCount: item.operateCount || '',
                  unitPrice: item.unitPrice || '',
                  unitPriceStr: item.unitPriceStr || '',
                  sumAmount: item.sumAmountStr || ''
                }))

                // 合并现有表格数据和新数据，避免重复添加
                const existingCodes = new Set(this.tableData.map((item) => item.materialCode))
                const uniqueNewMaterials = newMaterials.filter((item) => !existingCodes.has(item.materialCode))

                this.tableData = [...this.tableData, ...uniqueNewMaterials]
                this.$message.success(`已自动填充${uniqueNewMaterials.length}条耗材明细`)
              } else {
                this.$message.info('该出库单没有耗材明细数据')
              }
            }
          } catch (error) {
            console.error('获取出库单详情失败', error)
            this.$message.error('获取出库单详情失败')
          } finally {
            this.pageLoading = false
          }
        })
        .catch((error) => {
          console.error('获取出库单详情接口异常', error)
          this.$message.error('获取出库单详情失败')
          this.pageLoading = false
        })
    },
    // 修改批量删除方法
    handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      const selectedIds = new Set(this.multipleSelection.map((item) => item.id))
      this.tableData = this.tableData.filter((item) => !selectedIds.has(item.id))
    },
    // 新增删除操作方法
    onOperate(row) {
      const index = this.tableData.findIndex((item) => item === row)
      if (index !== -1) {
        this.tableData.splice(index, 1)
      }
    },
    handleQuantityChange(row) {
      // 自动计算金额（需要确保row中有materialUnitPrice字段）
      if (row.unitPrice && row.operateCount) {
        row.sumAmount = (row.unitPrice * row.operateCount).toFixed(2)
      } else {
        row.sumAmount = ''
      }
    },
    // 验证数量字段
    validateQuantity(row) {
      if (!row.operateCount) {
        this.$message.warning('入库数量不能为空')
        return false
      }
      if (row.operateCount <= 0) {
        this.$message.warning('入库数量必须大于0')
        row.operateCount = ''
        return false
      }
      // 确保数量为正整数
      row.operateCount = Math.abs(parseInt(row.operateCount))
      // 重新计算金额
      this.handleQuantityChange(row)
      return true
    },
    // 模板下载
    exportImportTemplete() {
      // 从store中获取用户信息，如果store中没有则从LOGINDATA中获取
      let hospitalCode, unitCode
      if (this.$store.getters['user/isLogin']) {
        const userInfo = this.$store.state.user.userInfo.user
        hospitalCode = userInfo.hospitalCode ?? 'BJSJTYY'
        unitCode = userInfo.unitCode ?? 'BJSYGJ'
      } else {
        const baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA') || '{}')
        hospitalCode = baseInfo.hospitalCode || 'BJSJTYY'
        unitCode = baseInfo.unitCode || 'BJSYGJ'
      }
      const data = {
        unitCode: unitCode,
        hospitalCode: hospitalCode
      }
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'inwarehouseRecord/exportImportTemplete',
        params: data,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        if (res.status == 200 && !res.data.code) {
          let name = '耗材模板.xls'
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          // let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          let url = URL.createObjectURL(blob)
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
          if (res.data.code == 500) {
            this.$message.error(res.data.message)
          } else if (res.status == 200 && !res.data.code) {
            this.$message.success('下载成功')
          } else {
            this.$message.error('下载失败')
          }
        }
      })
    },
    // 点击新增
    showaddConsumables() {
      this.exportCatch = 0
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
    },
    closeWorkOrderDialog() {
      this.workOrderVisible = false
    },
    closeWarehouseDialog() {
      this.warehouseVisible = false
    },

    // 根据出库单数据自动填充入库单信息
    autoFillFromOutWarehouse(outWarehouseData) {
      try {
        // 1. 自动填充入库仓库（使用出库单的仓库）
        if (outWarehouseData.warehouseId) {
          this.formInline.warehouseId = outWarehouseData.warehouseId
        }

        // 2. 自动填充申请人部门（使用出库单的申请人部门）
        if (outWarehouseData.applicantDepartmentId) {
          this.formInline.applicantDepartmentId = [outWarehouseData.applicantDepartmentId]
          // 加载该部门的人员列表
          this.getPersonList([outWarehouseData.applicantDepartmentId])
        }

        // 3. 自动填充申请人（使用出库单的申请人）
        if (outWarehouseData.applicantId) {
          // 延迟设置申请人，确保人员列表已加载
          this.$nextTick(() => {
            this.formInline.applicantId = outWarehouseData.applicantId
          })
        }

        // 4. 设置入库日期为当日
        const today = new Date()
        const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0')
        this.formInline.inwarehouseDate = todayStr

        // 提示用户已自动填充
        const filledFields = []
        if (outWarehouseData.warehouseId) filledFields.push('入库仓库')
        if (outWarehouseData.applicantDepartmentId) filledFields.push('申请人部门')
        if (outWarehouseData.applicantId) filledFields.push('申请人')
        filledFields.push('入库日期')

        if (filledFields.length > 0) {
          this.$message.success(`已自动填充：${filledFields.join('、')}`)
        }
      } catch (error) {
        console.error('自动填充出库单信息失败', error)
        this.$message.warning('自动填充部分信息失败，请手动检查')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}
.content_box {
  height: calc(100% - 30px);
  padding: 0 24px 36px 24px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}
.content-title {
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 24px;
  cursor: pointer;
}
.form-inline {
  .el-input,
  .el-select,
  .el-textarea .el-cascader {
    width: 300px;
  }
}
.form-inline .cascaderWid {
  width: 740px;
}
// 空内容样式
.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}
.green_line {
  display: inline-block;
  width: 5px;
  height: 16px;
  background: #3562db;
  margin-right: 6px;
  border-radius: 2px !important;
  vertical-align: middle;
}
.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  padding-left: 16px;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}
.el-form-item {
  margin-bottom: 16px;
}
</style>
